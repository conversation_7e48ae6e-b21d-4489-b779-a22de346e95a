# 开发规范和规则

- 用户要求完全移除项目中所有与 Milkdown 编辑器相关的实现代码和文件，但保留 Milkdown_使用教程.md 文档。不要生成总结性文档、测试脚本，不要编译或运行项目。
- 修复 Milkdown Crepe 编辑器 wikilink 插件注入问题：使用 crepe.editor.use() 方法注入插件，而不是直接在 crepe 实例上调用。正确的注入时机是在 Crepe 实例创建后，通过底层 editor 实例进行插件配置和注入。
- WikiLink 插件修复成功：核心问题是插件注入方式和 Remark 解析器冲突。解决方案：1) 使用 crepe.editor.use() 逐个注入插件 2) 使用 ctx.get(wikiLinkConfigCtx.key) 访问上下文 3) 暂时禁用 wikiLinkRemarkPlugin 避免解析冲突。输入规则功能正常工作。
- WikiLink 节点定义与 Crepe 编辑器冲突：问题根源是 WikiLink 节点的 schema 定义干扰了 Crepe 内置的链接解析。解决方案：需要重新设计节点定义，避免与 Crepe 的内置 schema 冲突，或者使用不同的实现方式。
- Crepe 编辑器 matchesNode 错误：问题不在 WikiLink 插件，而是文件内容中的异常格式（如转义的链接语法）导致 ProseMirror 解析失败。需要在文件加载前清理异常格式或使用更健壮的内容处理。
- 修复了Milkdown Crepe编辑器分屏布局问题：根本原因是编辑器初始化时错误地将placeholder作为defaultValue，导致编辑器同时显示占位符内容和实际文件内容。解决方案：1) 将defaultValue设置为空字符串 2) 通过专门的useEffect在编辑器就绪后设置初始内容 3) 避免内容冲突和重复渲染
- 修复了MarkdownEditor的三个关键问题：1) 导入缺失的getResourcesPath函数解决JavaScript错误 2) 合并重复的useEffect避免多次内容更新 3) 确认defaultValue为空字符串修复分屏布局。关键修改：导入utils.ts中的getResourcesPath，移除重复的内容更新逻辑，统一使用单个useEffect处理内容变化。
- 完成了MarkdownEditor组件的重大重构：基于Milkdown官方React集成最佳实践，将复杂的400+行代码简化为112行。使用@milkdown/react的MilkdownProvider和useEditor钩子，移除了手动DOM管理、复杂的useEffect依赖和重复的状态管理。新实现更稳定、更简洁，遵循官方推荐模式。
- 修复了MarkdownEditor重复更新问题：添加lastContentRef防重复更新机制，只有当initialValue真正变化时才更新编辑器内容。修复了markdownUpdated回调参数问题。增加了详细的日志输出帮助调试内容更新流程。
- WikiLink 跳转问题修复：根因是节点视图渲染器未被注入到编辑器中。修复方案：1) 创建 wikiLinkViewPlugin 将 WikiLinkNodeView 渲染器注入到编辑器 2) 在 MarkdownEditor 初始化时预先设置 WikiLink 配置 3) 按正确顺序注入所有 WikiLink 插件。关键是确保配置在插件创建时就可用，而不是后续通过 action 设置。
- WikiLink 完整功能修复完成：1) 跳转功能：通过 wikiLinkViewPlugin 注入 WikiLinkNodeView 渲染器实现点击处理 2) 预览功能：使用 $prose 包装预览插件解决类型问题，改进鼠标事件处理（ESC键隐藏、点击其他地方隐藏、优化延迟隐藏机制）3) 插件注入顺序：配置上下文 → 节点定义 → Remark解析 → 输入规则 → 预览功能 → 视图渲染。关键是确保配置在插件创建前设置好。
- WikiLink 预览功能最终优化：1) 完全移除title属性避免浏览器默认提示 2) 改进Markdown渲染，特别是列表处理逻辑 3) 实现所见即所得编辑：使用contenteditable替代textarea，添加HTML到Markdown双向转换，支持富文本编辑体验。关键是htmlToMarkdown和renderMarkdownToHtml方法的实现，确保编辑和预览的一致性。
- 用户要求完整的领域详情页优化方案：不要生成总结性文档、测试脚本，不要编译或运行项目。专注于代码实现和功能开发。
- 修复了ProjectKPI表缺少direction和frequency字段的数据库迁移问题：1) 在MigrationHelper中添加了needsProjectKPIDirectionMigration()方法检查字段是否存在；2) 创建了executeProjectKPIDirectionMigration()方法，分别检查并添加frequency和direction字段；3) 将ProjectKPI字段迁移集成到checkAndExecuteMigrations()主流程中；4) 从executeAreaMetricEnhancement()中移除了重复的ProjectKPI direction字段添加代码，避免冲突。
