# PaoLife 数据库字段修复脚本
# 直接通过SQLite命令添加缺失的字段

Write-Host "========================================" -ForegroundColor Green
Write-Host "PaoLife 数据库字段修复脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查SQLite是否可用
$sqliteCmd = Get-Command sqlite3 -ErrorAction SilentlyContinue
if (-not $sqliteCmd) {
    Write-Host "错误: 未找到 sqlite3 命令" -ForegroundColor Red
    Write-Host "请安装 SQLite 或使用其他方法" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 数据库文件路径
$dbPath = "prisma\dev.db"

if (-not (Test-Path $dbPath)) {
    Write-Host "错误: 未找到数据库文件 $dbPath" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "找到数据库文件: $dbPath" -ForegroundColor Yellow

# 备份数据库
$backupPath = "prisma\dev.db.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Copy-Item $dbPath $backupPath
Write-Host "数据库已备份到: $backupPath" -ForegroundColor Yellow

# 执行SQL命令添加字段
Write-Host "开始添加缺失的字段..." -ForegroundColor Yellow

$sqlCommands = @"
-- 添加 AreaMetric 表的缺失字段
ALTER TABLE AreaMetric ADD COLUMN trackingType TEXT NOT NULL DEFAULT 'metric';
ALTER TABLE AreaMetric ADD COLUMN habitConfig TEXT;
ALTER TABLE AreaMetric ADD COLUMN standardConfig TEXT;
ALTER TABLE AreaMetric ADD COLUMN isActive BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE AreaMetric ADD COLUMN priority TEXT;
ALTER TABLE AreaMetric ADD COLUMN category TEXT;
ALTER TABLE AreaMetric ADD COLUMN description TEXT;
ALTER TABLE AreaMetric ADD COLUMN direction TEXT NOT NULL DEFAULT 'increase';

-- 添加 ProjectKPI 表的缺失字段
ALTER TABLE ProjectKPI ADD COLUMN direction TEXT NOT NULL DEFAULT 'increase';

-- 添加 AreaMetricRecord 表的增强字段
ALTER TABLE AreaMetricRecord ADD COLUMN mood TEXT;
ALTER TABLE AreaMetricRecord ADD COLUMN energy TEXT;
ALTER TABLE AreaMetricRecord ADD COLUMN context TEXT;
ALTER TABLE AreaMetricRecord ADD COLUMN tags TEXT;
ALTER TABLE AreaMetricRecord ADD COLUMN quality TEXT;
ALTER TABLE AreaMetricRecord ADD COLUMN duration INTEGER;
ALTER TABLE AreaMetricRecord ADD COLUMN difficulty TEXT;
"@

# 将SQL命令写入临时文件
$tempSqlFile = "temp_migration.sql"
$sqlCommands | Out-File -FilePath $tempSqlFile -Encoding UTF8

try {
    # 执行SQL命令
    sqlite3 $dbPath ".read $tempSqlFile"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "字段添加成功！" -ForegroundColor Green
        
        # 验证字段是否添加成功
        Write-Host "验证 AreaMetric 表结构..." -ForegroundColor Yellow
        sqlite3 $dbPath "PRAGMA table_info(AreaMetric);"
        
        Write-Host "验证 ProjectKPI 表结构..." -ForegroundColor Yellow
        sqlite3 $dbPath "PRAGMA table_info(ProjectKPI);"
        
    } else {
        Write-Host "字段添加失败！" -ForegroundColor Red
    }
} catch {
    Write-Host "执行SQL命令时出错: $_" -ForegroundColor Red
} finally {
    # 清理临时文件
    if (Test-Path $tempSqlFile) {
        Remove-Item $tempSqlFile
    }
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "数据库修复完成！" -ForegroundColor Green
Write-Host "现在可以启动 PaoLife 应用程序了" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Read-Host "按任意键退出"
